CREATE TABLE IF NOT EXISTS tableDefinitions (
    rowid integer autoincrement start 1 increment 1,
    tablename varchar(100),
    keyfield varchar(100),
    datekeyfield varchar(100),
    version varchar(50),
    CONSTRAINT tabledefinitions_pkey PRIMARY KEY (rowid)
);

ALTER TABLE tableDefinitions ADD COLUMN version_new VARCHAR(50);


UPDATE tableDefinitions SET version_new = TO_VARCHAR(version);


ALTER TABLE tableDefinitions DROP COLUMN version;


ALTER TABLE tableDefinitions RENAME COLUMN version_new TO version;


MERGE INTO tableDefinitions AS target
USING (VALUES
    ('activeqmembersdata','keyid',NULL,1),
    ('activitycodedetails','keyid',NULL,1),
    ('adherenceactdata','keyid',NULL,1),
    ('adherencedaydata','keyid',NULL,1),
    ('adherenceexcdata','keyid',NULL,1),
    ('budetails','id',NULL,1),
    ('convsummarydata','keyid',NULL,1),
    ('convvoiceoverviewdata_backfill','keyid',NULL,2),
    ('convvoiceoverviewdata','keyid',NULL,2),
    ('convvoicesentimentdetaildata','keyid',NULL,2),
    ('convvoicetopicdetaildata','keyid',NULL,2),
    ('detailedinteractiondata','keyid',NULL,2),
    ('divisiondetails','id',NULL,1),
    ('evaldata','keyid',NULL,2),
    ('evaldetails','id',NULL,1),
    ('evalquestiondata','keyid',NULL,1),
    ('evalquestiongroupdata','keyid',NULL,1),
    ('flowoutcomedata','keyid',NULL,1), 
    ('flowoutcomedetails','id',NULL,1), 
    ('groupdetails','id',NULL,1),
    ('headcountforecastdata','keyid',NULL,2),
    ('hoursblockdata','keyid',NULL,1),
    ('knowledgebase', 'id', NULL,1),
    ('knowledgebasecategorydata', 'id', NULL,1),
    ('knowledgebasedocument', 'id', NULL,1),
    ('knowledgebasedocumentversion', 'id', NULL,1),
    ('mudetails','id',NULL,1),
    ('mumemberdata','id',NULL,1),
    ('chatdata','keyid',NULL,1),
    ('oauthusagedata','keyid',NULL,1),
    ('odcampaigndetails','id',NULL,1),
    ('odcontactlistdata','keyid',NULL,1),
    ('odcontactlistdetails','id',NULL,1),
    ('offeredforecastdata','keyid',NULL,1),
    ('participantattributesdynamic','keyid',NULL,1),
    ('participantsummarydata','keyid',NULL,1),
    ('planninggroupdetails','keyid',NULL,1),
    ('presencedetails','id',NULL,1),
    ('queueauditdata','keyid',NULL,1),
    ('queuedetails','id',NULL,1),
    ('queueinteractiondata','keyid',NULL,1),
    ('queueinteractiondata_backfill','keyid',NULL,1),
    ('queueinteractiondatadaily','keyid',NULL,1),
    ('queueinteractiondatamonthly','keyid',NULL,1),
    ('queueinteractiondataweekly','keyid',NULL,1),
    ('queueRealTimeConvData','keyid',NULL,1),
    ('scheduledata','keyid',NULL,1),
    ('scheduledetails','keyid',NULL,1),
    ('servicegoaldetails','id',NULL,1),
    ('skilldetails','id',NULL,1),
    ('suboverviewdata','keyid',NULL,1),
    ('subscriptiondata','keyid',NULL,1),
    ('licenseuserdata','keyid',NULL,1),
    ('surveydata','surveyid',NULL,1),
    ('tabledefinitions','rowid',NULL,1),
    ('teamdetails','id',NULL,1),
    ('teammemberdata','keyid',NULL,1),
    ('timeoffdata','keyid',NULL,1),
    ('timeoffrequestdata','keyid',NULL,1),
    ('userdetails','id',NULL,1),
    ('usergroupmappings','id',NULL,1),
    ('userinteractiondata','keyid',NULL,1),
    ('userinteractiondata_backfill','keyid',NULL,1),
    ('userinteractiondatadaily','keyid',NULL,1),
    ('userinteractiondatamonthly','keyid',NULL,1),
    ('userinteractiondataweekly','keyid',NULL,1),
    ('userinteractionpresencedetaileddata','keyid',NULL,NULL),
    ('userpresencedata','keyid',NULL,1),
    ('userpresencedata_backfill','keyid',NULL,1),
    ('userpresencedatadaily','keyid',NULL,1),
    ('userpresencedatamonthly','keyid',NULL,1),
    ('userpresencedataweekly','keyid',NULL,1),
    ('userpresencedetaileddata','keyid',NULL,1),
    ('userqueuemappings','keyid',NULL,1),
    ('userRealTimeConvData','keyid',NULL,1),
    ('userRealTimeData','id',NULL,1),
    ('userskillmappings','keyid',NULL,1),
    ('viewdefinitions','rowid',NULL,1),
    ('wfmauditdata','keyid',NULL,1),
    ('wrapupdetails','id',NULL,1),
    ('shrinkagedata','keyid',NULL,1),
    ('learningmodules','id',NULL,1),
	('learningmoduleassignments','id',NULL,1),
    ('learningassignmentresults','id',NULL,1)
) AS source (tablename, keyfield, datekeyfield, version)
ON target.tablename = source.tablename
WHEN NOT MATCHED THEN
    INSERT (tablename, keyfield)
    VALUES (source.tablename, source.keyfield);

BEGIN
    -- Delete rows in tabledefinitions that are not in the expected list
    DELETE FROM tabledefinitions
    WHERE tablename NOT IN (
        'activeqmembersdata', 
        'activitycodedetails', 
        'adherenceactdata', 
        'adherencedaydata', 
        'adherenceexcdata', 
        'budetails',
        'convsummarydata',
        'convvoiceoverviewdata_backfill', 
        'convvoiceoverviewdata', 
        'convvoicesentimentdetaildata', 
        'convvoicetopicdetaildata', 
        'detailedinteractiondata', 
        'divisiondetails', 
        'evaldata', 
        'evaldetails', 
        'evalquestiondata', 
        'evalquestiongroupdata', 
        'flowoutcomedata',
        'flowoutcomedetails',
        'groupdetails', 
        'headcountforecastdata', 
        'hoursblockdata', 
        'knowledgebase', 
        'knowledgebasecategorydata', 
        'knowledgebasedocument', 
        'knowledgebasedocumentversion', 
        'mudetails',
        'mumemberdata',
        'chatdata',
        'oauthusagedata',
        'odcampaigndetails', 
        'odcontactlistdata', 
        'odcontactlistdetails', 
        'offeredforecastdata', 
        'participantattributesdynamic', 
        'participantsummarydata', 
        'planninggroupdetails', 
        'presencedetails', 
        'queueauditdata', 
        'queuedetails', 
        'queueinteractiondata', 
        'queueinteractiondata_backfill', 
        'queueinteractiondatadaily', 
        'queueinteractiondatamonthly', 
        'queueinteractiondataweekly', 
        'queueRealTimeConvData', 
        'queueRealTimeData', 
        'scheduledata', 
        'scheduledetails', 
        'servicegoaldetails', 
        'skilldetails', 
        'suboverviewdata',
        'subscriptiondata',
        'licenseuserdata',
        'surveydata',
        'tabledefinitions', 
        'teamdetails', 
        'teammemberdata', 
        'timeoffdata', 
        'timeoffrequestdata', 
        'userdetails', 
        'usergroupmappings', 
        'userinteractiondata', 
        'userinteractiondata_backfill', 
        'userinteractiondatadaily', 
        'userinteractiondatamonthly', 
        'userinteractiondataweekly', 
        'userinteractionpresencedetaileddata', 
        'userpresencedata', 
        'userpresencedata_backfill', 
        'userpresencedatadaily', 
        'userpresencedatamonthly', 
        'userpresencedataweekly', 
        'userpresencedetaileddata', 
        'userqueuemappings', 
        'userRealTimeConvData', 
        'userRealTimeData', 
        'userskillmappings', 
        'viewdefinitions', 
        'wfmauditdata', 
        'wrapupdetails', 
        'shrinkagedata', 
        'learningmodules', 
        'learningmoduleassignments', 
        'learningassignmentresults'
    );

    -- Output confirmation message
    RETURN 'Deleted rows successfully.';
END;

-- Conditionally add subuserusagedata to tabledefinitions only if the table exists
-- This handles the deprecation scenario where we don't want to track deprecated tables in new installations
CREATE OR REPLACE PROCEDURE handle_subuserusagedata_tabledefinitions()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  try {
    // Check if table exists
    var checkTableQuery = `SELECT COUNT(*) as table_count
                          FROM information_schema.tables
                          WHERE table_name = 'SUBUSERUSAGEDATA'`;
    var resultSet = snowflake.execute({sqlText: checkTableQuery});
    resultSet.next();
    var tableExists = resultSet.getColumnValue(1) > 0;

    if (tableExists) {
      // Table exists, add it to tabledefinitions if not already present
      var checkDefQuery = `SELECT COUNT(*) as def_count
                          FROM tabledefinitions
                          WHERE tablename = 'subuserusagedata'`;
      var defResultSet = snowflake.execute({sqlText: checkDefQuery});
      defResultSet.next();
      var defExists = defResultSet.getColumnValue(1) > 0;

      if (!defExists) {
        snowflake.execute({
          sqlText: `INSERT INTO tabledefinitions (tablename, keyfield, datekeyfield, version)
                   VALUES ('subuserusagedata', 'keyid', NULL, 1)`
        });
      }

      return 'subuserusagedata table exists - added to tabledefinitions';
    } else {
      // Table doesn't exist, remove it from tabledefinitions if present
      snowflake.execute({
        sqlText: `DELETE FROM tabledefinitions WHERE tablename = 'subuserusagedata'`
      });

      return 'subuserusagedata table does not exist - removed from tabledefinitions';
    }
  } catch (e) {
    return 'Error handling subuserusagedata tabledefinitions: ' + e.message;
  }
$$;

-- Execute the procedure
CALL handle_subuserusagedata_tabledefinitions();

