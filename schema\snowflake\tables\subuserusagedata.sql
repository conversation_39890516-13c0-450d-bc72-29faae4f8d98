CREATE TABLE IF NOT EXISTS subuserusagedata (
    keyid varchar(100) NOT NULL,
    date timestamp without time zone,
    userlogin varchar(200),
    licensename varchar(200),
    secs numeric(20, 2),
    hoursstr varchar(50),
    updated timestamp without time zone,
    CONSTRAINT subuserusagedata_pkey PRIMARY KEY (keyid)
);

-- Add deprecation status column for API deprecation tracking
ALTER TABLE subuserusagedata
ADD COLUMN IF NOT EXISTS deprecated_status varchar(50) DEFAULT 'active';

-- Add deprecation date column to track when the table was deprecated
ALTER TABLE subuserusagedata
ADD COLUMN IF NOT EXISTS deprecated_date timestamp without time zone DEFAULT '2025-03-10 00:00:00';