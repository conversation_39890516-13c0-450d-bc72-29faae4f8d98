using System;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Static utility class for handling JSON API actions and deprecation detection
    /// </summary>
    public static class JsonActions
    {
        /// <summary>
        /// Makes an HTTP GET request and returns both status code and response content
        /// This method includes deprecation detection and throws appropriate exceptions
        /// </summary>
        /// <param name="uri">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <param name="logger">Optional logger for logging deprecation warnings</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        /// <exception cref="GenesysApiDeprecatedException">Thrown when the API endpoint is deprecated</exception>
        public static HttpApiResponse JsonReturnHttpResponseGet(string uri, string apiKey, ILogger? logger = null)
        {
            var jsonUtils = new JsonUtils(logger);
            var response = jsonUtils.JsonReturnHttpResponseGet(uri, apiKey);

            // Check for deprecation and throw exception if detected
            if (response.IsDeprecationNotice)
            {
                var deprecationDate = ExtractDeprecationDate(response.Content);
                var message = $"Genesys Cloud API endpoint has been deprecated: {uri}. " +
                             $"HTTP {response.StatusCode}: {response.StatusDescription}. " +
                             $"Response: {response.Content}";

                logger?.LogError("API Deprecation Detected: {Message}", message);

                throw new GenesysApiDeprecatedException(
                    message,
                    uri,
                    response.StatusCode,
                    response.Content,
                    deprecationDate
                );
            }

            return response;
        }

        /// <summary>
        /// Makes an HTTP POST request and returns both status code and response content
        /// This method includes deprecation detection and throws appropriate exceptions
        /// </summary>
        /// <param name="uri">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <param name="requestBody">The JSON body to send</param>
        /// <param name="logger">Optional logger for logging deprecation warnings</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        /// <exception cref="GenesysApiDeprecatedException">Thrown when the API endpoint is deprecated</exception>
        public static async Task<HttpApiResponse> JsonReturnHttpResponsePostAsync(string uri, string apiKey, string requestBody, ILogger? logger = null)
        {
            var jsonUtils = new JsonUtils(logger);
            var response = await jsonUtils.JsonReturnHttpResponseAsync(uri, apiKey, requestBody);

            // Check for deprecation and throw exception if detected
            if (response.IsDeprecationNotice)
            {
                var deprecationDate = ExtractDeprecationDate(response.Content);
                var message = $"Genesys Cloud API endpoint has been deprecated: {uri}. " +
                             $"HTTP {response.StatusCode}: {response.StatusDescription}. " +
                             $"Response: {response.Content}";

                logger?.LogError("API Deprecation Detected: {Message}", message);

                throw new GenesysApiDeprecatedException(
                    message,
                    uri,
                    response.StatusCode,
                    response.Content,
                    deprecationDate
                );
            }

            return response;
        }

        /// <summary>
        /// Attempts to extract a deprecation date from the API response content
        /// </summary>
        /// <param name="responseContent">The API response content</param>
        /// <returns>The deprecation date if found, otherwise null</returns>
        private static DateTime? ExtractDeprecationDate(string responseContent)
        {
            if (string.IsNullOrWhiteSpace(responseContent))
                return null;

            // Look for common deprecation date patterns
            var content = responseContent.ToLowerInvariant();
            
            // Check for specific known deprecation dates
            if (content.Contains("march 10, 2025") || content.Contains("2025-03-10"))
            {
                return new DateTime(2025, 3, 10);
            }

            // Could add more sophisticated date parsing here if needed
            return null;
        }

        /// <summary>
        /// Checks if a response indicates a deprecated API without throwing an exception
        /// </summary>
        /// <param name="response">The HTTP API response to check</param>
        /// <returns>True if the response indicates deprecation</returns>
        public static bool IsDeprecatedApiResponse(HttpApiResponse response)
        {
            return response?.IsDeprecationNotice ?? false;
        }

        /// <summary>
        /// Creates a standardized deprecation error message
        /// </summary>
        /// <param name="uri">The deprecated API endpoint</param>
        /// <param name="statusCode">HTTP status code</param>
        /// <param name="responseContent">Response content</param>
        /// <returns>Formatted deprecation error message</returns>
        public static string CreateDeprecationErrorMessage(string uri, int statusCode, string responseContent)
        {
            return $"API endpoint deprecated: {uri}. HTTP {statusCode}. " +
                   $"This endpoint is no longer supported by Genesys Cloud. " +
                   $"Response: {responseContent}";
        }
    }
}
