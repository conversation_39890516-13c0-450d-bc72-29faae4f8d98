IF dbo.csg_table_exists('tableDefinitions') = 0
CREATE TABLE [tableDefinitions](
    [rowid] [int] IDENTITY(1, 1) NOT NULL,
    [tablename] [nvarchar](50),
    [keyfield] [nvarchar](50),
    [datekeyfield] [nvarchar](50),
    [version] [nvarchar](50),
    CONSTRAINT [PK__tmp_ms_x__4B5BD7F884744217] PRIMARY KEY ([rowid])
);
ALTER TABLE tableDefinitions
ALTER COLUMN version [nvarchar](50);

MERGE INTO tableDefinitions AS target
USING (VALUES
    ('activeqmembersdata','keyid',NULL,1),
    ('activitycodedetails','keyid',NULL,1),
    ('adherenceactdata','keyid',NULL,1),
    ('adherenceactdata_backfill','keyid',NULL,1),
    ('adherencedaydata','keyid',NULL,1),
    ('adherencedaydata_backfill','keyid',NULL,1),
    ('adherenceexcdata','keyid',NULL,1),
    ('adherenceexcdata_backfill','keyid',NULL,1),
    ('budetails','id',NULL,1),
    ('convsummarydata','keyid',NULL,1),
    ('convsummarydata_backfill','keyid',NULL,1),
    ('convvoiceoverviewdata_backfill','keyid',NULL,2),
    ('convvoiceoverviewdata','keyid',NULL,2),
    ('convvoicesentimentdetaildata','keyid',NULL,2),
    ('convvoicetopicdetaildata','keyid',NULL,2),
    ('detailedinteractiondata','keyid',NULL,2),
    ('detailedinteractiondata_backfill','keyid',NULL,2),
    ('divisiondetails','id',NULL,1),
    ('evaldata','keyid',NULL,2),
    ('evaldata_backfill','keyid',NULL,2),
    ('evaldetails','id',NULL,1),
    ('evalquestiondata','keyid',NULL,1),
    ('evalquestiondata_backfill','keyid',NULL,1),
    ('evalquestiongroupdata','keyid',NULL,1),
    ('evalquestiongroupdata_backfill','keyid',NULL,1),
    ('flowoutcomedata','keyid',NULL,1),
    ('flowoutcomedetails','id',NULL,1),
    ('groupdetails','id',NULL,1),
    ('headcountforecastdata','keyid',NULL,2),
    ('headcountforecastdata_backfill','keyid',NULL,2),
    ('hoursblockdata','keyid',NULL,1),
    ('hoursblockdata_backfill','keyid',NULL,1),
    ('knowledgebase', 'id', NULL,1),
    ('knowledgebasecategorydata', 'id', NULL,1),
    ('knowledgebasedocument', 'id', NULL,1),
    ('knowledgebasedocumentversion', 'id', NULL,1),
    ('mudetails','id',NULL,1),
    ('mumemberdata','id',NULL,1),
    ('chatdata','keyid',NULL,1),
    ('oauthusagedata','keyid',NULL,1),
    ('odcampaigndetails','id',NULL,1),
    ('odcontactlistdata','keyid',NULL,1),
    ('odcontactlistdetails','id',NULL,1),
    ('offeredforecastdata','keyid',NULL,1),
    ('participantattributesdynamic','keyid',NULL,1),
    ('participantattributesdynamic_backfill','keyid',NULL,1),
    ('participantsummarydata','keyid',NULL,1),
    ('participantsummarydata_backfill','keyid',NULL,1),
    ('planninggroupdetails','keyid',NULL,1),
    ('presencedetails','id',NULL,1),
    ('queueauditdata','keyid',NULL,1),
    ('queueauditdata_backfill','keyid',NULL,1),
    ('queuedetails','id',NULL,1),
    ('queueinteractiondata','keyid',NULL,1),
    ('queueinteractiondata_backfill','keyid',NULL,1),
    ('queueinteractiondatadaily','keyid',NULL,1),
    ('queueinteractiondatamonthly','keyid',NULL,1),
    ('queueinteractiondataweekly','keyid',NULL,1),
    ('queueRealTimeConvData','keyid',NULL,1),
    ('queueRealTimeData','keyid',NULL,1),
    ('scheduledata','keyid',NULL,1),
    ('scheduledata_backfill','keyid',NULL,1),
    ('scheduledetails','keyid',NULL,1),
    ('scheduledetails_backfill','keyid',NULL,1),
    ('servicegoaldetails','id',NULL,1),
    ('skilldetails','id',NULL,1),
    ('suboverviewdata','keyid',NULL,1),
    ('subscriptiondata','keyid',NULL,1),
    ('licenseuserdata','keyid',NULL,1),
    ('surveydata','surveyid',NULL,1),
    ('tabledefinitions','rowid',NULL,1),
    ('teamdetails','id',NULL,1),
    ('teammemberdata','keyid',NULL,1),
    ('timeoffdata','keyid',NULL,1),
    ('timeoffdata_backfill','keyid',NULL,1),
    ('timeoffrequestdata','keyid',NULL,1),
    ('userdetails','id',NULL,1),
    ('usergroupmappings','id',NULL,1),
    ('userinteractiondata','keyid',NULL,1),
    ('userinteractiondata_backfill','keyid',NULL,1),
    ('userinteractiondatadaily','keyid',NULL,1),
    ('userinteractiondatamonthly','keyid',NULL,1),
    ('userinteractiondataweekly','keyid',NULL,1),
    ('userinteractionpresencedetaileddata',NULL,NULL,NULL),
    ('userpresencedata','keyid',NULL,1),
    ('userpresencedata_backfill','keyid',NULL,1),
    ('userpresencedatadaily','keyid',NULL,1),
    ('userpresencedatamonthly','keyid',NULL,1),
    ('userpresencedataweekly','keyid',NULL,1),
    ('userpresencedetaileddata','keyid',NULL,1),
    ('userpresencedetaileddata_backfill','keyid',NULL,1),
    ('userqueuemappings','keyid',NULL,1),
    ('userRealTimeConvData','keyid',NULL,1),
    ('userRealTimeData','id',NULL,1),
    ('userskillmappings','keyid',NULL,1),
    ('viewdefinitions','rowid',NULL,1),
    ('wfmauditdata','keyid',NULL,1),
    ('wfmauditdata_backfill','keyid',NULL,1),
    ('wrapupdetails','id',NULL,1),
    ('shrinkagedata','keyid',NULL,1),
    ('learningmodules','id',NULL,1),
    ('learningmoduleassignments','id',NULL,1),
    ('learningassignmentresults','id',NULL,1)
) AS source (tablename, keyfield, datekeyfield, version)
ON target.tablename = source.tablename
WHEN NOT MATCHED THEN
    INSERT (tablename, keyfield)
    VALUES (source.tablename, source.keyfield);

BEGIN
    -- Delete rows in tabledefinitions that are not in the expected list
    DELETE FROM tabledefinitions
    WHERE tablename NOT IN (
        'activeqmembersdata',
        'activitycodedetails',
        'adherenceactdata',
        'adherenceactdata_backfill',
        'adherencedaydata',
        'adherencedaydata_backfill',
        'adherenceexcdata',
        'adherenceexcdata_backfill',
        'budetails',
        'convsummarydata',
        'convsummarydata_backfill',
        'convvoiceoverviewdata_backfill',
        'convvoiceoverviewdata',
        'convvoicesentimentdetaildata',
        'convvoicetopicdetaildata',
        'detailedinteractiondata',
        'detailedinteractiondata_backfill',
        'divisiondetails',
        'evaldata',
        'evaldata_backfill',
        'evaldetails',
        'evalquestiondata',
        'evalquestiondata_backfill',
        'evalquestiongroupdata',
        'evalquestiongroupdata_backfill',
        'flowoutcomedata',
        'flowoutcomedetails',
        'groupdetails',
        'headcountforecastdata',
        'headcountforecastdata_backfill',
        'hoursblockdata',
        'hoursblockdata_backfill',
        'knowledgebase',
        'knowledgebasecategorydata',
        'knowledgebasedocument',
        'knowledgebasedocumentversion',
        'mudetails',
        'mumemberdata',
        'chatdata',
        'oauthusagedata',
        'odcampaigndetails',
        'odcontactlistdata',
        'odcontactlistdetails',
        'offeredforecastdata',
        'participantattributesdynamic',
        'participantattributesdynamic_backfill',
        'participantsummarydata',
        'participantsummarydata_backfill',
        'planninggroupdetails',
        'presencedetails',
        'queueauditdata',
        'queueauditdata_backfill',
        'queuedetails',
        'queueinteractiondata',
        'queueinteractiondata_backfill',
        'queueinteractiondatadaily',
        'queueinteractiondatamonthly',
        'queueinteractiondataweekly',
        'queueRealTimeConvData',
        'queueRealTimeData',
        'scheduledata',
        'scheduledata_backfill',
        'scheduledetails',
        'scheduledetails_backfill',
        'servicegoaldetails',
        'skilldetails',
        'suboverviewdata',
        'subscriptiondata',
        'licenseuserdata',
        'surveydata',
        'tabledefinitions',
        'teamdetails',
        'teammemberdata',
        'timeoffdata',
        'timeoffdata_backfill',
        'timeoffrequestdata',
        'userdetails',
        'usergroupmappings',
        'userinteractiondata',
        'userinteractiondata_backfill',
        'userinteractiondatadaily',
        'userinteractiondatamonthly',
        'userinteractiondataweekly',
        'userinteractionpresencedetaileddata',
        'userpresencedata',
        'userpresencedata_backfill',
        'userpresencedatadaily',
        'userpresencedatamonthly',
        'userpresencedataweekly',
        'userpresencedetaileddata',
        'userpresencedetaileddata_backfill',
        'userqueuemappings',
        'userRealTimeConvData',
        'userRealTimeData',
        'userskillmappings',
        'viewdefinitions',
        'wfmauditdata',
        'wfmauditdata_backfill',
        'wrapupdetails',
        'shrinkagedata',
        'learningmodules',
        'learningmoduleassignments',
        'learningassignmentresults'
    );

    -- Output confirmation message
    PRINT 'Deleted rows successfully.';
END;

-- Conditionally add subuserusagedata to tabledefinitions only if the table exists
-- This handles the deprecation scenario where we don't want to track deprecated tables in new installations
IF dbo.csg_table_exists('subuserusageData') = 1
BEGIN
    -- Table exists, add it to tabledefinitions if not already present
    IF NOT EXISTS (SELECT 1 FROM tabledefinitions WHERE tablename = 'subuserusagedata')
    BEGIN
        INSERT INTO tabledefinitions (tablename, keyfield, datekeyfield, version)
        VALUES ('subuserusagedata', 'keyid', NULL, 1);
        PRINT 'subuserusagedata table exists - added to tabledefinitions';
    END
END
ELSE
BEGIN
    -- Table doesn't exist, remove it from tabledefinitions if present
    DELETE FROM tabledefinitions WHERE tablename = 'subuserusagedata';
    PRINT 'subuserusagedata table does not exist - removed from tabledefinitions';
END
