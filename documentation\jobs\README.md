# Genesys Adapter Jobs Documentation

This directory contains detailed documentation for each job type supported by the Genesys Adapter. Each job has its own documentation file with comprehensive information about its purpose, data flow, configuration, and troubleshooting.

## Job Categories

### Administrative Jobs
- [Users](Users.md) - User and group synchronization
- [QueueMembership](QueueMembership.md) - Queue membership management
- [LicenseUsers](LicenseUsers.md) - License assignment tracking
- [OAuthUsage](OAuthUsage.md) - API usage monitoring

### Interaction Data Jobs
- [Interaction](Interaction.md) - Detailed conversation data
- [Chat](Chat.md) - Chat conversation analytics
- [Message](Message.md) - Message conversation analytics
- [VoiceAnalysis](VoiceAnalysis.md) - Voice analytics and sentiment
- [Evaluation](Evaluation.md) - Quality evaluation data

### Workforce Management Jobs
- [WFMSchedule](WFMSchedule.md) - Agent schedule data
- [ScheduleDetails](ScheduleDetails.md) - Detailed schedule information
- [Adherence](Adherence.md) - Schedule adherence tracking
- [WFMAudit](WFMAudit.md) - WFM audit trail
- [TimeOffReq](TimeOffReq.md) - Time-off request management

### Real-time Data Jobs
- [Realtime](Realtime.md) - Live queue and user metrics
- [PresenceDetail](PresenceDetail.md) - Detailed presence tracking

### Reference Data Jobs
- [FactData](FactData.md) - Reference data synchronization
- [KnowledgeBaseDetails](KnowledgeBaseDetails.md) - Knowledge base configuration
- [LearningDataDetails](LearningDataDetails.md) - Learning module configuration

### Outbound Dialing Jobs
- [ODContactLists](ODContactLists.md) - Contact list management
- [ODDetails](ODDetails.md) - Outbound dialing configuration

### Forecasting Jobs
- [HeadCountForecast](HeadCountForecast.md) - Staffing predictions
- [OfferedForecast](OfferedForecast.md) - Queue forecast data

### Utility Jobs
- [Aggregation](Aggregation.md) - Data aggregation processes
- [Install](Install.md) - Database schema installation
- [Information](Information.md) - System information display

### Deprecated Jobs
- [SubsUsers](SubsUsers.md) - ⚠️ **DEPRECATED** - Legacy subscription data

## Documentation Structure

Each job documentation file includes:

1. **Overview** - Purpose and description
2. **Data Flow Diagram** - Mermaid chart showing the complete data flow
3. **Configuration** - Required settings and options
4. **Database Tables** - Tables populated by the job
5. **API Endpoints** - Genesys Cloud APIs used
6. **Dependencies** - Prerequisites and related jobs
7. **Monitoring** - Key metrics and logging
8. **Troubleshooting** - Common issues and solutions
9. **Examples** - Configuration and usage examples

## Quick Reference

| Job | Type | Frequency | Tables | APIs |
|-----|------|-----------|--------|------|
| Users | Admin | Daily | userdetails, groupdetails | /api/v2/users |
| Realtime | Real-time | Continuous | queuerealtimedata | WebSocket |
| VoiceAnalysis | Interaction | Hourly | convvoiceoverviewdata | /api/v2/analytics/conversations |
| WFMSchedule | WFM | Daily | scheduledata | /api/v2/workforcemanagement/schedules |

For detailed information about any specific job, click on the job name above or navigate to the corresponding documentation file.
