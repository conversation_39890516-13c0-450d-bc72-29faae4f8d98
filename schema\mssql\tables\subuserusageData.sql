IF dbo.csg_table_exists('subuserusageData') = 0
CREATE TABLE [subuserusageData](
    [keyid] [nvarchar](50) NOT NULL,
    [date] [date],
    [userlogin] [nvarchar](50),
    [licensename] [nvarchar](200),
    [secs] [decimal](20, 2),
    [hoursstr] [nvarchar](50),
    [updated] [datetime],
    CONSTRAINT [PK_subuserusageData] PRIMARY KEY ([keyid])
);

-- Add deprecation status column for API deprecation tracking
IF dbo.csg_column_exists('subuserusageData', 'deprecated_status') = 0
    ALTER TABLE subuserusageData ADD deprecated_status [nvarchar](50) DEFAULT 'active';

-- Add deprecation date column to track when the table was deprecated
IF dbo.csg_column_exists('subuserusageData', 'deprecated_date') = 0
    ALTER TABLE subuserusageData ADD deprecated_date [datetime] DEFAULT '2025-03-10 00:00:00';
