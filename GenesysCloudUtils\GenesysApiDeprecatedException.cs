using System;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Exception thrown when a Genesys Cloud API endpoint has been deprecated
    /// </summary>
    public class GenesysApiDeprecatedException : Exception
    {
        public string EndpointUrl { get; }
        public int HttpStatusCode { get; }
        public string ApiResponse { get; }
        public DateTime? DeprecationDate { get; }

        public GenesysApiDeprecatedException(string message, string endpointUrl, int httpStatusCode, string apiResponse, DateTime? deprecationDate = null) 
            : base(message)
        {
            EndpointUrl = endpointUrl;
            HttpStatusCode = httpStatusCode;
            ApiResponse = apiResponse;
            DeprecationDate = deprecationDate;
        }

        public GenesysApiDeprecatedException(string message, string endpointUrl, int httpStatusCode, string apiResponse, Exception innerException, DateTime? deprecationDate = null) 
            : base(message, innerException)
        {
            EndpointUrl = endpointUrl;
            HttpStatusCode = httpStatusCode;
            ApiResponse = apiResponse;
            DeprecationDate = deprecationDate;
        }
    }
}
