CREATE TABLE IF NOT EXISTS subuserusagedata (
    keyid varchar(100) NOT NULL,
    date timestamp without time zone,
    userlogin varchar(50),
    licensename varchar(200),
    secs numeric(20, 2),
    hoursstr varchar(50),
    updated timestamp without time zone,
    CONSTRAINT subuserusagedata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

-- Add deprecation status column for API deprecation tracking
ALTER TABLE subuserusagedata
ADD COLUMN IF NOT EXISTS deprecated_status varchar(50) DEFAULT 'active';

-- Add deprecation date column to track when the table was deprecated
ALTER TABLE subuserusagedata
ADD COLUMN IF NOT EXISTS deprecated_date timestamp without time zone DEFAULT '2025-03-10 00:00:00';

COMMENT ON COLUMN subuserusageData.date IS 'Date';
COMMENT ON COLUMN subuserusageData.keyid IS 'Primary Key';
COMMENT ON COLUMN subuserusageData.licensename IS 'License Used';
COMMENT ON COLUMN subuserusageData.secs IS 'Time in Secs';
COMMENT ON COLUMN subuserusageData.updated IS 'Date Row Updated (UTC)';
COMMENT ON COLUMN subuserusageData.userlogin IS 'User GUID';
COMMENT ON COLUMN subuserusageData.hoursstr IS ' ';
COMMENT ON COLUMN subuserusageData.deprecated_status IS 'Deprecation status of the table (active/deprecated)';
COMMENT ON COLUMN subuserusageData.deprecated_date IS 'Date when the underlying API was deprecated (2025-03-10)';
COMMENT ON TABLE subuserusageData IS 'DEPRECATED: Subscription Detailed Data - No longer updated due to Genesys Cloud API deprecation (March 10, 2025). Historical data preserved for reference.';