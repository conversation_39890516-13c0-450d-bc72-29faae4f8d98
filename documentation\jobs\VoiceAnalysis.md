# Voice Analysis Job Documentation

## Overview

The Voice Analysis job retrieves and processes voice conversation data from Genesys Cloud, including sentiment analysis, topic detection, and transcript processing. It provides comprehensive analytics for voice interactions to support quality management and business intelligence.

## Data Flow Diagram

```mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        CONV[Conversations API<br/>/api/v2/analytics/conversations]
        TRANS[Transcripts API<br/>/api/v2/conversations/{id}/recordings]
        SENT[Sentiment Analysis<br/>Built-in Analytics]
        TOPIC[Topic Detection<br/>Built-in Analytics]
    end
    
    subgraph "Voice Analysis Process"
        VA[GCUpdateInteractionData<br/>Voice Analysis Controller]
        FETCH[Data Fetcher<br/>Conversation Retrieval]
        PROC[Analytics Processor<br/>Sentiment & Topics]
        TRANS_PROC[Transcript Processor<br/>Text Analysis]
        DIFF[Diffing Engine<br/>Change Detection]
    end
    
    subgraph "Database Tables"
        OVERVIEW[(convvoiceoverviewdata<br/>Voice Conversation Overview)]
        SENTIMENT[(convvoicesentimentdetaildata<br/>Sentiment Analysis Results)]
        TOPICS[(convvoicetopicdetaildata<br/>Topic Detection Results)]
    end
    
    subgraph "Knowledge Quest Integration"
        KQ_CHECK[Queue Verification<br/>verify-queue endpoint]
        KQ_INGEST[Transcript Ingestion<br/>ingest-transcript endpoint]
        KQ_LICENSE[License Check<br/>Knowledge_Quest Type]
    end
    
    %% Main flow
    VA --> FETCH
    FETCH --> CONV
    CONV --> PROC
    PROC --> SENT
    PROC --> TOPIC
    PROC --> TRANS_PROC
    TRANS_PROC --> TRANS
    
    %% Diffing and storage
    PROC --> DIFF
    DIFF --> OVERVIEW
    DIFF --> SENTIMENT
    DIFF --> TOPICS
    
    %% Knowledge Quest flow
    VA --> KQ_LICENSE
    KQ_LICENSE --> KQ_CHECK
    KQ_CHECK --> KQ_INGEST
    TRANS_PROC --> KQ_INGEST
    
    %% Styling
    classDef api fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef knowledge fill:#f3e5f5
    
    class CONV,TRANS,SENT,TOPIC api
    class VA,FETCH,PROC,TRANS_PROC,DIFF process
    class OVERVIEW,SENTIMENT,TOPICS database
    class KQ_CHECK,KQ_INGEST,KQ_LICENSE knowledge
```

## Configuration

### Required Settings
- **GenesysApi**: OAuth credentials with analytics permissions
- **Database**: Connection string for target database
- **DateRange**: Time period for analysis (default: last 24 hours)

### Optional Settings
- **KnowledgeQuest**: Enable Knowledge Quest integration
- **TranscriptProcessing**: Enable transcript download and processing
- **BatchSize**: Records per processing batch (default: 50)
- **Backfill**: Process historical data

### Example Configuration
```json
{
  "Job": "VoiceAnalysis",
  "GenesysApi": {
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "Region": "us-east-1"
  },
  "Preferences": {
    "Backfill": false,
    "KnowledgeQuest": true
  }
}
```

## Database Tables

### convvoiceoverviewdata
- **Purpose**: Main voice conversation metrics and metadata
- **Key Fields**: conversationid, duration, participantcount, direction
- **Indexes**: conversationid (PK), conversationstarttime
- **Update Logic**: Upsert based on conversation ID

### convvoicesentimentdetaildata
- **Purpose**: Sentiment analysis results per conversation segment
- **Key Fields**: conversationid, segmentstart, sentimentscore, sentimenttrend
- **Indexes**: conversationid, segmentstart
- **Update Logic**: Replace all segments per conversation

### convvoicetopicdetaildata
- **Purpose**: Topic detection results and confidence scores
- **Key Fields**: conversationid, topicname, confidence, occurrences
- **Indexes**: conversationid, topicname
- **Update Logic**: Replace all topics per conversation

## API Endpoints

### Primary APIs
- `/api/v2/analytics/conversations/details/query` - Conversation data
- `/api/v2/conversations/{conversationId}/recordings` - Transcript access
- `/api/v2/analytics/conversations/aggregates/query` - Aggregated metrics

### Knowledge Quest APIs (Optional)
- `POST /verify-queue` - Queue eligibility verification
- `POST /ingest-transcript` - Transcript submission for analysis

## Dependencies

### Prerequisites
- **Conversation Job**: Must run first to populate base conversation data
- **Users Job**: Provides participant information
- **Queue Membership**: Links conversations to queues
- **Valid License**: Knowledge Quest features require appropriate licensing

### Related Jobs
- **Interaction**: Provides base conversation data
- **Evaluation**: May use voice analysis results
- **FactData**: Provides queue and user reference data

## Processing Logic

### Data Collection
1. Query conversations from specified date range
2. Filter for voice media type only
3. Retrieve sentiment and topic analysis data
4. Download transcripts if enabled

### Analysis Processing
1. Extract sentiment scores and trends
2. Identify topics and confidence levels
3. Process transcript text for Knowledge Quest
4. Apply diffing logic to detect changes

### Knowledge Quest Integration
1. Verify queue eligibility for Knowledge Quest
2. Check if transcript already processed
3. Submit eligible transcripts for ingestion
4. Track processing status

## Monitoring

### Key Metrics
- **Conversations Processed**: Total voice conversations analyzed
- **Sentiment Coverage**: Percentage with sentiment data
- **Topic Coverage**: Percentage with topic detection
- **Transcript Success Rate**: Successful transcript downloads
- **Knowledge Quest Submissions**: Transcripts sent for analysis

### Performance Indicators
- **Processing Rate**: Conversations per minute
- **API Response Time**: Average API call duration
- **Error Rate**: Failed API calls or processing errors
- **Data Freshness**: Time lag from conversation end to analysis

## Troubleshooting

### Common Issues

#### Missing Sentiment Data
- **Symptoms**: Empty sentiment scores in database
- **Causes**: Conversations too short, no speech detected
- **Solutions**: Check conversation duration, verify audio quality

#### Transcript Download Failures
- **Symptoms**: Missing transcript data, download errors
- **Causes**: Permissions, recording not available, API limits
- **Solutions**: Verify recording permissions, check API quotas

#### Knowledge Quest Integration Issues
- **Symptoms**: Transcripts not being processed
- **Causes**: License validation, queue not eligible, API errors
- **Solutions**: Verify license type, check queue configuration

### Error Handling
- **Rate Limiting**: Automatic retry with exponential backoff
- **API Failures**: Log errors and continue with next batch
- **Data Validation**: Skip invalid records with detailed logging

## Performance Optimization

### Best Practices
- Use appropriate date ranges to avoid large datasets
- Enable diffing to process only changed data
- Monitor API rate limits and adjust batch sizes
- Cache transcript data to avoid re-downloading

### Scaling Considerations
- Large contact centers may need longer processing windows
- Consider parallel processing for high-volume environments
- Monitor database performance for large sentiment/topic datasets

## Examples

### Basic Voice Analysis
```bash
GenesysAdapter --job VoiceAnalysis
```

### Historical Backfill
```bash
GenesysAdapter --job VoiceAnalysis --preferences.backfill true
```

### With Knowledge Quest
```json
{
  "Job": "VoiceAnalysis",
  "Preferences": {
    "KnowledgeQuest": true,
    "TranscriptProcessing": true
  }
}
```

### Custom Date Range
```json
{
  "Job": "VoiceAnalysis",
  "Preferences": {
    "StartDate": "2024-01-01T00:00:00Z",
    "EndDate": "2024-01-31T23:59:59Z"
  }
}
```
