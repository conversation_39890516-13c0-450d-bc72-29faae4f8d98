# Genesys Cloud Data Adapter

Synchronises Genesys metrics (both real-time and historical) to a database allowing use by business analytics applications.
[[_TOC_]]

## Architecture Overview

The Genesys Adapter is a comprehensive data synchronization solution that connects Genesys Cloud with various database platforms. The following diagrams illustrate the system architecture and data flow.

> **Note**: These Mermaid diagrams are maintained as part of the codebase documentation and are updated whenever significant architectural changes are made to ensure they accurately reflect the current system design.

### System Architecture

```mermaid
graph TB
    subgraph "External Systems"
        GC[Genesys Cloud APIs]
        DB[(Database<br/>MSSQL/PostgreSQL/Snowflake)]
        LIC[License Validation API]
    end

    subgraph "Genesys Adapter Core"
        GA[GenesysAdapter<br/>Main Application]

        subgraph "Data Processing Layer"
            GCD[GCData<br/>Data Retrieval]
            GCFD[GCFactData<br/>Fact Data Processing]
            GCRT[GCRealTime<br/>Real-time Processing]
        end

        subgraph "Utility Layer"
            GCU[GenesysCloudUtils<br/>API Communication]
            DBU[DBUtils<br/>Database Operations]
            SU[StandardUtils<br/>Common Utilities]
            GCAC[GCACommon<br/>Configuration]
        end
    end

    subgraph "Job Types"
        ADM[Admin Jobs<br/>Users, Queues, etc.]
        INT[Interaction Jobs<br/>Conversations, Voice]
        WFM[WFM Jobs<br/>Schedules, Adherence]
        RT[Real-time Jobs<br/>Live Metrics]
        FACT[Fact Jobs<br/>Reference Data]
    end

    %% External connections
    GA --> GC
    GA --> DB
    GA --> LIC

    %% Internal dependencies
    GA --> GCD
    GA --> GCFD
    GA --> GCRT
    GA --> GCAC

    GCD --> GCU
    GCD --> DBU
    GCFD --> GCU
    GCFD --> DBU
    GCRT --> GCU
    GCRT --> DBU

    GCU --> SU
    DBU --> SU

    %% Job flows
    GA --> ADM
    GA --> INT
    GA --> WFM
    GA --> RT
    GA --> FACT

    %% Styling
    classDef external fill:#e1f5fe
    classDef core fill:#f3e5f5
    classDef data fill:#e8f5e8
    classDef utility fill:#fff3e0
    classDef jobs fill:#fce4ec

    class GC,DB,LIC external
    class GA core
    class GCD,GCFD,GCRT data
    class GCU,DBU,SU,GCAC utility
    class ADM,INT,WFM,RT,FACT jobs
```

### Project Dependencies

```mermaid
graph TD
    subgraph "Main Application"
        GA[GenesysAdapter]
    end

    subgraph "Data Processing Projects"
        GCD[GCData<br/>Data Retrieval & Processing]
        GCFD[GCFactData<br/>Fact Data Management]
        GCRT[GCRealTime<br/>Real-time Data Processing]
    end

    subgraph "Utility Projects"
        GCU[GenesysCloudUtils<br/>Genesys Cloud API Client]
        DBU[DBUtils<br/>Database Abstraction Layer]
        SU[StandardUtils<br/>Common Utilities]
        GCAC[GCACommon<br/>Configuration & Options]
    end

    subgraph "External Dependencies"
        NET[.NET 6 Runtime]
        NPGSQL[Npgsql<br/>PostgreSQL Driver]
        MSSQL[SqlClient<br/>SQL Server Driver]
        SNOW[Snowflake.Data<br/>Snowflake Driver]
        JSON[Newtonsoft.Json]
        LOG[Microsoft.Extensions.Logging]
    end

    %% Project dependencies
    GA --> GCD
    GA --> GCFD
    GA --> GCRT
    GA --> GCU
    GA --> DBU
    GA --> SU
    GA --> GCAC

    GCD --> GCU
    GCD --> DBU
    GCD --> SU
    GCD --> GCAC

    GCFD --> GCU
    GCFD --> DBU
    GCFD --> SU
    GCFD --> GCAC

    GCRT --> GCU
    GCRT --> DBU
    GCRT --> SU
    GCRT --> GCAC

    GCU --> SU
    GCU --> GCAC

    DBU --> SU
    DBU --> GCAC

    %% External dependencies
    GA --> NET
    GA --> LOG

    DBU --> NPGSQL
    DBU --> MSSQL
    DBU --> SNOW

    GCU --> JSON
    SU --> JSON

    %% Styling
    classDef main fill:#e3f2fd
    classDef data fill:#e8f5e8
    classDef utility fill:#fff3e0
    classDef external fill:#fafafa

    class GA main
    class GCD,GCFD,GCRT data
    class GCU,DBU,SU,GCAC utility
    class NET,NPGSQL,MSSQL,SNOW,JSON,LOG external
```

## Documentation

- [General Documentation](documentation/README.md)
- [Database Documentation](documentation/database/README.md)
  - [Database Functions](documentation/database/schema/DatabaseFunctionDocumentation.md)
  - [Schema Templates](documentation/database/schema/SchemaTemplates.md)
  - [Schema Review Checklist](documentation/database/schema/SchemaReviewChecklist.md)
  - [Schema Function Analysis Report](documentation/database/schema/SchemaFunctionAnalysisReport.md)
  - [Schema Function Analyzer](documentation/database/schema/SchemaFunctionAnalyzer.ps1)

## Build and Test

| Dev | Prod |
|-----|------|
| [![Build Status](https://dev.azure.com/customerscience/technology/_apis/build/status/genesys-adapter?branchName=dev)](https://dev.azure.com/customerscience/technology/_build/latest?definitionId=2&branchName=dev) | [![Build Status](https://dev.azure.com/customerscience/technology/_apis/build/status/genesys-adapter?branchName=master)](https://dev.azure.com/customerscience/technology/_build/latest?definitionId=2&branchName=master) |

### Development Practises

* [GitFlow](https://nvie.com/posts/a-successful-git-branching-model/) branching model
* [C# Coding Style](https://github.com/dotnet/runtime/blob/main/docs/coding-guidelines/coding-style.md)
* [Clean Code concepts](https://github.com/thangchung/clean-code-dotnet)
* Commit messages follow [Conventional Commits](https://www.conventionalcommits.org/)

### Build

[Nuke](https://nuke.build) is used to generate the Azure build pipeline and also allows cross platform local execution
of the pipeline.

```powershell
./build.ps1 --help
./build.ps1
```

```bash
./build.sh --help
./build.sh
```

Install the nuke tool to allow execution using the `nuke` command.

```sh
dotnet nuke --help
```

Local building can also be done with the normal dotnet tools.

```sh
dotnet build
dotnet test
dotnet run
dotnet publish -c Release -r linux-musl-x64 -p:PublishSingleFile=True --self-contained --no-restore GenesysAdapter/GenesysAdapter.csproj
```

### dotnet local tools

Install the default dotnet local tools by running the command below. This will install nuke, dotnet-outdated, etc.

```sh
dotnet tool restore
```

### Podman

The pipeline will build a Docker image, it is recommended to use [Podman](https://podman.io/) for local testing.
Nuke does a locate on docker, so setting an alias to podman is not enough, creating a symlink is required to use Podman
in the Nuke pipeline.

* DockerHub doesn't support OCI images so need to set BUILDAH_FORMAT, [DockerHub #1871](https://github.com/docker/hub-feedback/issues/1871)

#### Setting alternate / symbolic link

```bash
sudo update-alternatives --install /usr/local/bin/docker docker /usr/bin/podman 20
```

```powershell
New-Item -ItemType SymbolicLink -Path:(Split-Path (Get-Command -Name podman.exe).Source) -Name:"docker.exe" -Target (Get-Command -Name podman.exe).Source
```

### Updating package dependencies

* [dotnet-outdated](https://github.com/dotnet-outdated/dotnet-outdated)

Check for updates:

```powershell
dotnet dotnet-outdated [--upgrade]
```

## Database Schema Management

The Genesys Adapter supports multiple database types (MSSQL, PostgreSQL, and Snowflake), each with its own set of schema files and utility functions.

### Database Functions

The adapter uses a set of utility functions to manage database objects consistently across different database types:

- `csg_table_exists`: Checks if a table exists
- `csg_column_exists`: Checks if a column exists in a table
- `csg_index_exists`: Checks if an index exists on a table
- `csg_constraint_exists`: Checks if a constraint exists on a table
- And many more...

These functions are defined in the `installfunctions.sql` file for each database type. See the [Database Function Documentation](documentation/database/schema/DatabaseFunctionDocumentation.md) for details.

### Schema Templates

To ensure consistency when creating new schema files, use the templates provided in the [Schema Templates](documentation/database/schema/SchemaTemplates.md) document. These templates include examples for creating tables, adding columns, creating indexes, and adding foreign keys for each database type.

### Best Practices

When working with the database schema, follow these best practices:

1. **Use existence checks**: Always check if objects exist before creating or modifying them.
2. **Consistent naming**: Use consistent naming conventions for tables, columns, indexes, and constraints.
3. **Documentation**: Include comments explaining the purpose of tables, columns, and complex logic.
4. **Error handling**: Implement proper error handling in procedures and functions.
5. **Database-specific features**: Leverage the specific features of each database type.
6. **Cross-database consistency**: Ensure that table and column definitions are consistent across database types.

See the [Schema Review Checklist](documentation/database/schema/SchemaReviewChecklist.md) for a comprehensive list of best practices.

### Database Architecture

```mermaid
graph TB
    subgraph "Multi-Database Support"
        MSSQL[(Microsoft SQL Server)]
        POSTGRES[(PostgreSQL)]
        SNOWFLAKE[(Snowflake)]
    end

    subgraph "Database Abstraction Layer"
        DBU[DBUtils<br/>Database Operations]
        CONN[Connection Management]
        BULK[Bulk Operations]
        DIFF[Diffing Logic]
    end

    subgraph "Schema Management"
        SCHEMA[Schema Files<br/>Per Database Type]
        TABLES[Table Definitions]
        VIEWS[View Definitions]
        FUNCS[Functions & Procedures]
        INDEXES[Index Definitions]
    end

    subgraph "Core Database Tables"
        subgraph "Configuration"
            TD[(tabledefinitions<br/>Schema Metadata)]
            VD[(viewdefinitions<br/>View Metadata)]
        end

        subgraph "User & Organization"
            UD[(userdetails<br/>User Information)]
            QD[(queuedetails<br/>Queue Configuration)]
            BD[(budetails<br/>Business Units)]
            GD[(groupdetails<br/>User Groups)]
        end

        subgraph "Interaction Data"
            CD[(conversationdata<br/>Call Records)]
            VCD[(convvoiceoverviewdata<br/>Voice Analytics)]
            CHAT[(chatdata<br/>Chat Interactions)]
            ED[(evaldata<br/>Quality Evaluations)]
        end

        subgraph "Workforce Management"
            SD[(scheduledata<br/>Agent Schedules)]
            SDD[(scheduledetails<br/>Schedule Details)]
            AD[(adherencedata<br/>Schedule Adherence)]
            WA[(wfmauditdata<br/>WFM Changes)]
        end

        subgraph "Real-time Data"
            QRT[(queuerealtimedata<br/>Live Queue Metrics)]
            URT[(userrealtimedata<br/>Live User Status)]
            CRT[(queuerealtimeconvdata<br/>Live Conversations)]
        end

        subgraph "License & Usage"
            LUD[(licenseuserdata<br/>License Assignments)]
            OUD[(oauthusagedata<br/>API Usage)]
            SUD[(subuserusagedata<br/>Historical Usage)]
        end
    end

    subgraph "Data Processing Features"
        BATCH[Batch Processing<br/>Large Data Sets]
        INCR[Incremental Updates<br/>Delta Processing]
        BACKFILL[Backfill Operations<br/>Historical Data]
        COMPRESS[Data Compression<br/>Archive Tables]
    end

    %% Database connections
    DBU --> MSSQL
    DBU --> POSTGRES
    DBU --> SNOWFLAKE

    %% Schema management
    SCHEMA --> TABLES
    SCHEMA --> VIEWS
    SCHEMA --> FUNCS
    SCHEMA --> INDEXES

    %% Database operations
    DBU --> CONN
    DBU --> BULK
    DBU --> DIFF

    %% Data processing
    DBU --> BATCH
    DBU --> INCR
    DBU --> BACKFILL
    DBU --> COMPRESS

    %% Table relationships (key examples)
    UD -.-> QD
    UD -.-> CD
    QD -.-> CD
    BD -.-> UD
    BD -.-> QD

    %% Styling
    classDef database fill:#e1f5fe
    classDef abstraction fill:#e8f5e8
    classDef schema fill:#fff3e0
    classDef config fill:#f3e5f5
    classDef user fill:#fce4ec
    classDef interaction fill:#e0f2f1
    classDef wfm fill:#fff8e1
    classDef realtime fill:#fde7f3
    classDef license fill:#e8eaf6
    classDef processing fill:#f1f8e9

    class MSSQL,POSTGRES,SNOWFLAKE database
    class DBU,CONN,BULK,DIFF abstraction
    class SCHEMA,TABLES,VIEWS,FUNCS,INDEXES schema
    class TD,VD config
    class UD,QD,BD,GD user
    class CD,VCD,CHAT,ED interaction
    class SD,SDD,AD,WA wfm
    class QRT,URT,CRT realtime
    class LUD,OUD,SUD license
    class BATCH,INCR,BACKFILL,COMPRESS processing
```

### API Integration and Error Handling

```mermaid
graph TB
    subgraph "Genesys Cloud API Layer"
        AUTH[OAuth Authentication<br/>/api/v2/oauth/token]
        RATE[Rate Limiting<br/>300 requests/token]
        RETRY[Retry Logic<br/>Exponential Backoff]
        DEPRECATION[Deprecation Detection<br/>GenesysApiDeprecatedException]
    end

    subgraph "API Communication Components"
        GCU[GenesysCloudUtils<br/>API Client]
        JU[JsonUtils<br/>HTTP Operations]
        JA[JsonActions<br/>Deprecation Handling]
        HR[HttpApiResponse<br/>Response Processing]
    end

    subgraph "Error Handling Framework"
        EH[Exception Hierarchy]
        GDAE[GenesysApiDeprecatedException<br/>Deprecated APIs]
        RLE[Rate Limit Exceptions<br/>429 Errors]
        PE[Permission Exceptions<br/>403 Errors]
        NRE[Network/Retry Exceptions<br/>Transient Failures]
    end

    subgraph "API Endpoints by Category"
        subgraph "Core APIs"
            USERS_API[Users API<br/>/api/v2/users]
            QUEUES_API[Queues API<br/>/api/v2/routing/queues]
            CONV_API[Conversations API<br/>/api/v2/analytics/conversations]
        end

        subgraph "WFM APIs"
            WFM_SCHED[Schedules<br/>/api/v2/workforcemanagement/schedules]
            WFM_ADHER[Adherence<br/>/api/v2/workforcemanagement/adherence]
            WFM_AUDIT[Audit<br/>/api/v2/workforcemanagement/audits]
        end

        subgraph "License & Billing"
            LICENSE_API[License Users<br/>/api/v2/license/users]
            DEPRECATED_BILLING[❌ Billing API<br/>~~hourlyLicenseUsageData~~]
        end

        subgraph "Real-time"
            WS_NOTIF[WebSocket Notifications<br/>Real-time Events]
            CHANNELS[Notification Channels<br/>/api/v2/notifications/channels]
        end
    end

    subgraph "Data Processing Pipeline"
        FETCH[API Data Fetching]
        VALIDATE[Response Validation]
        TRANSFORM[Data Transformation]
        DIFF[Change Detection]
        PERSIST[Database Persistence]
    end

    %% API Layer connections
    AUTH --> GCU
    RATE --> GCU
    RETRY --> GCU
    DEPRECATION --> GCU

    %% Component relationships
    GCU --> JU
    JU --> JA
    JU --> HR
    JA --> GDAE

    %% Error handling
    GCU --> EH
    EH --> GDAE
    EH --> RLE
    EH --> PE
    EH --> NRE

    %% API endpoint connections
    GCU --> USERS_API
    GCU --> QUEUES_API
    GCU --> CONV_API
    GCU --> WFM_SCHED
    GCU --> WFM_ADHER
    GCU --> WFM_AUDIT
    GCU --> LICENSE_API
    GCU --> WS_NOTIF
    GCU --> CHANNELS

    %% Deprecation handling
    DEPRECATED_BILLING -.-> GDAE
    GDAE -.-> LICENSE_API

    %% Processing pipeline
    GCU --> FETCH
    FETCH --> VALIDATE
    VALIDATE --> TRANSFORM
    TRANSFORM --> DIFF
    DIFF --> PERSIST

    %% Error flows
    VALIDATE -.-> RLE
    VALIDATE -.-> PE
    VALIDATE -.-> GDAE
    FETCH -.-> NRE

    %% Styling
    classDef api fill:#e1f5fe
    classDef component fill:#e8f5e8
    classDef error fill:#ffebee
    classDef core fill:#fff3e0
    classDef wfm fill:#f3e5f5
    classDef license fill:#e8eaf6
    classDef realtime fill:#fce4ec
    classDef deprecated fill:#ffcdd2
    classDef pipeline fill:#e0f2f1

    class AUTH,RATE,RETRY,DEPRECATION api
    class GCU,JU,JA,HR component
    class EH,GDAE,RLE,PE,NRE error
    class USERS_API,QUEUES_API,CONV_API core
    class WFM_SCHED,WFM_ADHER,WFM_AUDIT wfm
    class LICENSE_API license
    class WS_NOTIF,CHANNELS realtime
    class DEPRECATED_BILLING deprecated
    class FETCH,VALIDATE,TRANSFORM,DIFF,PERSIST pipeline
```

### Job Flow Architecture

```mermaid
graph TB
    subgraph "Genesys Cloud APIs"
        AUTH[Authentication<br/>/api/v2/oauth/token]
        USERS[Users API<br/>/api/v2/users]
        QUEUES[Queues API<br/>/api/v2/routing/queues]
        CONV[Conversations API<br/>/api/v2/analytics/conversations]
        WS[WebSocket<br/>Real-time Notifications]
        WFM_API[WFM APIs<br/>/api/v2/workforcemanagement]
        LICENSE[License API<br/>/api/v2/license/users]
    end

    subgraph "Job Categories"
        subgraph "Administrative Jobs"
            ADMIN_USERS[Users & Groups]
            ADMIN_QUEUES[Queue Membership]
            ADMIN_LICENSE[License Users]
            ADMIN_OAUTH[OAuth Usage]
        end

        subgraph "Interaction Jobs"
            CONV_DATA[Conversation Data]
            VOICE_ANALYSIS[Voice Analysis]
            CHAT_DATA[Chat Data]
            EVAL_DATA[Evaluation Data]
        end

        subgraph "WFM Jobs"
            SCHEDULE[WFM Schedule]
            SCHEDULE_DETAILS[Schedule Details]
            ADHERENCE[Adherence]
            WFM_AUDIT[WFM Audit]
        end

        subgraph "Real-time Jobs"
            RT_QUEUE[Queue Real-time]
            RT_USER[User Real-time]
            RT_CONV[Conversation Real-time]
        end

        subgraph "Fact Data Jobs"
            FACT_ALL[All Reference Data]
            BU_DETAILS[Business Units]
            ACTIVITY_CODES[Activity Codes]
            SKILL_DETAILS[Skills]
        end
    end

    subgraph "Database Tables"
        subgraph "Admin Tables"
            T_USERS[(userdetails)]
            T_QUEUES[(queuedetails)]
            T_LICENSE[(licenseuserdata)]
            T_OAUTH[(oauthusagedata)]
        end

        subgraph "Interaction Tables"
            T_CONV[(conversationdata)]
            T_VOICE[(convvoiceoverviewdata)]
            T_CHAT[(chatdata)]
            T_EVAL[(evaldata)]
        end

        subgraph "WFM Tables"
            T_SCHED[(scheduledata)]
            T_SCHED_DET[(scheduledetails)]
            T_ADHER[(adherencedata)]
            T_WFM_AUDIT[(wfmauditdata)]
        end

        subgraph "Real-time Tables"
            T_RT_Q[(queuerealtimedata)]
            T_RT_U[(userrealtimedata)]
            T_RT_C[(queuerealtimeconvdata)]
        end

        subgraph "Fact Tables"
            T_BU[(budetails)]
            T_ACT[(activitycodedetails)]
            T_SKILL[(skilldetails)]
        end
    end

    %% API to Job mappings
    AUTH --> ADMIN_LICENSE
    USERS --> ADMIN_USERS
    QUEUES --> ADMIN_QUEUES
    CONV --> CONV_DATA
    CONV --> VOICE_ANALYSIS
    WS --> RT_QUEUE
    WS --> RT_USER
    WS --> RT_CONV
    WFM_API --> SCHEDULE
    WFM_API --> SCHEDULE_DETAILS
    WFM_API --> ADHERENCE
    WFM_API --> WFM_AUDIT
    LICENSE --> ADMIN_LICENSE

    %% Job to Table mappings
    ADMIN_USERS --> T_USERS
    ADMIN_QUEUES --> T_QUEUES
    ADMIN_LICENSE --> T_LICENSE
    ADMIN_OAUTH --> T_OAUTH

    CONV_DATA --> T_CONV
    VOICE_ANALYSIS --> T_VOICE
    CHAT_DATA --> T_CHAT
    EVAL_DATA --> T_EVAL

    SCHEDULE --> T_SCHED
    SCHEDULE_DETAILS --> T_SCHED_DET
    ADHERENCE --> T_ADHER
    WFM_AUDIT --> T_WFM_AUDIT

    RT_QUEUE --> T_RT_Q
    RT_USER --> T_RT_U
    RT_CONV --> T_RT_C

    FACT_ALL --> T_BU
    BU_DETAILS --> T_BU
    ACTIVITY_CODES --> T_ACT
    SKILL_DETAILS --> T_SKILL

    %% Styling
    classDef api fill:#e1f5fe
    classDef admin fill:#e8f5e8
    classDef interaction fill:#fff3e0
    classDef wfm fill:#f3e5f5
    classDef realtime fill:#fce4ec
    classDef fact fill:#e0f2f1
    classDef table fill:#fafafa

    class AUTH,USERS,QUEUES,CONV,WS,WFM_API,LICENSE api
    class ADMIN_USERS,ADMIN_QUEUES,ADMIN_LICENSE,ADMIN_OAUTH,T_USERS,T_QUEUES,T_LICENSE,T_OAUTH admin
    class CONV_DATA,VOICE_ANALYSIS,CHAT_DATA,EVAL_DATA,T_CONV,T_VOICE,T_CHAT,T_EVAL interaction
    class SCHEDULE,SCHEDULE_DETAILS,ADHERENCE,WFM_AUDIT,T_SCHED,T_SCHED_DET,T_ADHER,T_WFM_AUDIT wfm
    class RT_QUEUE,RT_USER,RT_CONV,T_RT_Q,T_RT_U,T_RT_C realtime
    class FACT_ALL,BU_DETAILS,ACTIVITY_CODES,SKILL_DETAILS,T_BU,T_ACT,T_SKILL fact
```

## Synchronisation Jobs

This section provides details about the various synchronization jobs available in the Genesys Adapter.

> **📖 Detailed Documentation**: For comprehensive information about each job including data flow diagrams, configuration options, troubleshooting guides, and examples, see the [Jobs Documentation](documentation/jobs/README.md) folder.

### Quick Reference

| Job | Documentation | Type | Description |
|-----|---------------|------|-------------|
| **Realtime** | [📖 Details](documentation/jobs/Realtime.md) | Real-time | Live queue and user metrics via WebSocket |
| **VoiceAnalysis** | [📖 Details](documentation/jobs/VoiceAnalysis.md) | Interaction | Voice analytics, sentiment, and topics |
| **LicenseUsers** | [📖 Details](documentation/jobs/LicenseUsers.md) | Admin | Current license assignments |
| **WFMSchedule** | [📖 Details](documentation/jobs/WFMSchedule.md) | WFM | Agent schedule data |
| **FactData** | [📖 Details](documentation/jobs/FactData.md) | Reference | Organizational reference data |
| **SubsUsers** | [📖 Details](documentation/jobs/SubsUsers.md) | ⚠️ Deprecated | Legacy subscription data |

### Agent Performance Jobs

#### Adherence
- **Description**: Synchronizes agent adherence to published schedules
- **Tables**:
  - adherencedaydata
  - adherenceexcdata
  - adherenceactdata
- **API**: `/api/v2/workforcemanagement/adherence/historical/bulk`

#### Evaluation
- **Description**: Retrieves evaluation data with conversation ID, evaluation ID, status, scores, and related data
- **Tables**:
  - evaldata
  - evalquestiondata
  - evalquestiongroupdata
- **APIs**:
  - `/api/v2/analytics/evaluations/aggregates/query`
  - `/api/v2/quality/evaluations`
- **Note**: Materialized views for evaluation data are updated by database procedures scheduled via cron jobs:
  - `update_mvwevaluationgroupdata`: Updates the evaluation group materialized view
  - Cron jobs also refresh `mvwevaluationoverview` and `mvwevaluationquestiondata` materialized views

#### EvaluationCatchup
- **Description**: Updates existing pending evaluations that may have changed since the last evaluation job run
- **Tables**: evaldata
- **API**: `/api/v2/quality/evaluations`

#### Shrinkage
- **Description**: Retrieves agent shrinkage data including scheduled and actual shrinkage metrics
- **Tables**: shrinkagedata
- **APIs**:
  - `/api/v2/workforcemanagement/managementunits`
  - `/api/v2/workforcemanagement/managementunits/{managementUnitId}/shrinkage/jobs`

#### Survey
- **Description**: Retrieves customer survey data including scores and completion status
- **Tables**: surveydata
- **API**: `/api/v2/analytics/surveys/aggregates/query`

### Interaction Data Jobs

#### Aggregation
- **Description**: Aggregates user presence and interaction data
- **Tables**:
  - userpresencedata
  - userinteractiondata
  - queueinteractiondata
- **APIs**:
  - `/api/v2/analytics/users/aggregates/query`
  - `/api/v2/analytics/conversations/aggregates/query`
- **Note**: Daily, weekly, and monthly aggregation tables (userpresencedatadaily, userinteractiondataweekly, etc.) are generated by database procedures scheduled via cron jobs. These procedures include:
  - `archiveuserpresence`: Aggregates presence data into daily/weekly/monthly tables
  - `archiveuserinteraction`: Aggregates user interaction data into daily/weekly/monthly tables
  - `archivequeueinteraction`: Aggregates queue interaction data into daily/weekly/monthly tables
  - `archivebacklog`: Runs all archive procedures for multiple time periods

#### Chat
- **Description**: Retrieves and analyzes chat conversation data, storing metrics like chat counts, durations, and response times
- **Tables**: chatdata (with mediatype='chat')
- **API**: `/api/v2/analytics/conversations/details/query`
- **Note**: Uses unified storage with Message job in the chatdata table

#### Message
- **Description**: Retrieves and analyzes message conversation data, storing metrics like message counts, durations, and response times
- **Tables**: chatdata (with mediatype='message')
- **API**: `/api/v2/analytics/conversations/details/query`
- **Note**: Uses unified storage with Chat job in the chatdata table for gradual transition from legacy chat functionality

#### Interaction
- **Description**: Retrieves detailed interaction data including conversation summaries, participant details, and custom attributes
- **Tables**:
  - convsummarydata
  - detailedinteractiondata
  - participantattributesdynamic
  - participantsummarydata
- **APIs**:
  - `/api/v2/analytics/conversations/details/query`
  - `/api/v2/analytics/conversations/details/jobs`

#### InteractionPresence
- **Description**: Combines interaction and presence data for detailed agent activity analysis
- **Tables**: userinteractionpresencedetaileddata
- **APIs**:
  - `/api/v2/analytics/users/details/query`
  - `/api/v2/analytics/conversations/details/query`

#### PresenceDetail
- **Description**: Retrieves detailed user presence data including system presence, routing status, and durations
- **Tables**: userpresencedetaileddata
- **API**: `/api/v2/analytics/users/details/query`

#### VoiceAnalysis → [📖 Detailed Documentation](documentation/jobs/VoiceAnalysis.md)
- **Description**: Retrieves voice analytics data including sentiment analysis, topics, and transcripts
- **Tables**:
  - convvoiceoverviewdata
  - convvoicesentimentdetaildata
  - convvoicetopicdetaildata
- **APIs**:
  - `/api/v2/analytics/conversations/details/query`
  - `/api/v2/speechandtextanalytics/conversations/{conversationId}`
- **Note**: Materialized views for voice analytics data are updated by database procedures scheduled via cron jobs:
  - `update_mvwconvvoiceoverviewdata`: Updates the voice overview materialized view
  - `update_mvwconvvoicesentimentdetaildata`: Updates the voice sentiment materialized view
  - `update_mvwconvvoicetopicdetaildata`: Updates the voice topic materialized view

### Workforce Management Jobs

#### HeadCountForecast
- **Description**: Retrieves predicted headcount requirements from schedules in 15-minute intervals
- **Tables**: headcountforecastdata
- **API**: `/api/v2/workforcemanagement/schedules`

#### HoursBlockData
- **Description**: Creates timesheet data with blocks of hours, breaking out time spent on breaks and meetings
- **Tables**: hoursblockdata
- **Uses data from**: scheduledetails

#### ScheduleDetails
- **Description**: Retrieves detailed schedule information for agents including shifts, activities, and time allocations
- **Tables**: scheduledetails
- **API**: `/api/v2/workforcemanagement/schedules`

#### WFMSchedule → [📖 Detailed Documentation](documentation/jobs/WFMSchedule.md)
- **Description**: Retrieves workforce management schedule data for long-term planning and analysis
- **Tables**: scheduledata
- **API**: `/api/v2/workforcemanagement/schedules`

#### TimeOffReq
- **Description**: Retrieves time-off requests for agents
- **Tables**: timeoffrequestdata
- **API**: `/api/v2/workforcemanagement/timeoffrequests`

#### WFMAudit
- **Description**: Retrieves workforce management audit data for tracking changes to schedules and configurations
- **Tables**: wfmauditdata
- **API**: `/api/v2/workforcemanagement/audits`



### Queue Management Jobs

#### OfferedForecast
- **Description**: Retrieves queue estimated wait times and forecasted metrics
- **Tables**: offeredforecastdata
- **API**: `/api/v2/routing/queues/{queueId}/estimatedwaittime`

#### QueueMembership
- **Description**: Retrieves active queue membership data showing which agents are assigned to which queues
- **Tables**: activeqmembersdata
- **API**: `/api/v2/routing/queues/{queueId}/members`

#### UserQueueAudit
- **Description**: Tracks changes to queue configurations and membership over time
- **Tables**: queueauditdata
- **APIs**:
  - `/api/v2/routing/queues`
  - `/api/v2/routing/queues/{queueId}/members`

#### UserQueueMapping
- **Description**: Maps users to queues they are assigned to
- **Tables**: userqueuemappings
- **APIs**:
  - `/api/v2/routing/queues/{queueId}/members`
  - `/api/v2/users/{userId}/queues`

### Outbound Dialing Jobs

#### ODContactLists
- **Description**: Retrieves outbound dialing contact list data, downloads a CSV and dynamically adds columns
- **Tables**: odcontactlistdata
- **API**: `/api/v2/outbound/contactlists/{contactListId}/export`

#### ODDetails
- **Description**: Retrieves outbound dialing configuration data including contact lists and campaign details
- **Tables**:
  - odcontactlistdetails (includes contact list modified date)
  - odcampaigndetails
- **APIs**:
  - `/api/v2/outbound/contactlists`
  - `/api/v2/outbound/campaigns`

### Real-time Data Jobs

#### Realtime → [📖 Detailed Documentation](documentation/jobs/Realtime.md)
- **Description**: Retrieves real-time metrics for queues and users via WebSocket connections, updating data continuously
- **Tables**:
  - queuerealtimedata
  - queuerealtimeconvdata
  - userrealtimedata
  - userrealtimeconvdata
- **APIs**:
  - WebSocket notifications
  - `/api/v2/notifications/channels`
  - `/api/v2/analytics/conversations/details`

#### Subscription ⚠️ **DEPRECATED**
- **Status**: **DECOMMISSIONED** - This table has been removed as it was unused
- **Description**: Previously intended for managing notification subscriptions but was never actively populated
- **Tables**: ~~subscriptiondata~~ (removed)
- **Reason**: The subscriptiondata table was identified as orphaned with no active jobs populating it
- **Impact**: No functional impact as this table was not being used by any active processes

#### LicenseUsers → [📖 Detailed Documentation](documentation/jobs/LicenseUsers.md)
- **Description**: Retrieves current license assignment and user data for monitoring license utilization and user management
- **Tables**: licenseuserdata
- **API**: `/api/v2/license/users`
- **Features**:
  - Real-time license assignment tracking
  - User license utilization monitoring
  - Supports diffing for efficient updates
  - Replaces deprecated SubsUsers functionality
- **Status**: Active and recommended for license management

#### SubsUsers ⚠️ **DEPRECATED** → [📖 Detailed Documentation](documentation/jobs/SubsUsers.md)
- **Status**: **DECOMMISSIONED** - This job has been disabled due to Genesys Cloud API deprecation
- **Deprecation Date**: March 10, 2025
- **Reason**: The underlying Genesys Cloud API endpoint `/api/v2/billing/reports/hourlyLicenseUsageData` was deprecated as part of Genesys Cloud's billing infrastructure modernization
- **Description**: Previously retrieved user subscription data for monitoring user activity and license usage
- **Tables**: subuserusagedata (historical data preserved, no longer updated)
- **API**: ~~`/api/v2/billing/reports/hourlyLicenseUsageData`~~ (deprecated)
- **Reference**: [Genesys Cloud Deprecation Notice](https://help.mypurecloud.com/announcements/deprecation-billing-and-usage-apis-and-user-interface/)
- **Impact**: Historical subscription usage data remains available in the database but no new data will be collected

### System Management Jobs

#### Information
- **Description**: Displays information about the Genesys Adapter configuration and available jobs
- **Tables**: None (informational only)
- **API**: None (uses local configuration)

#### Install
- **Description**: Installs or updates the database schema and initializes the system
- **Tables**: All schema tables
- **API**: Various endpoints for permissions setup

### Reference Data Jobs

#### FactData → [📖 Detailed Documentation](documentation/jobs/FactData.md)
- **Description**: Synchronizes reference data like activity codes, business units, divisions, groups, etc.
- **Tables**: Various lookup tables (activitycodedetails, budetails, divisiondetails, etc.)
- **APIs**: Multiple endpoints for reference data

#### KnowledgeBaseDetails
- **Description**: Retrieves knowledge base configuration data including categories and documents
- **Tables**:
  - knowledgebase
  - knowledgebasecategorydata
- **API**: `/api/v2/knowledge/knowledgebases`

#### LearningDataDetails
- **Description**: Retrieves learning module configuration data including assignments
- **Tables**:
  - learningmodules
  - learningmoduleassignments
- **API**: `/api/v2/learning/modules`

#### OAuthUsage
- **Description**: Retrieves OAuth usage data for monitoring API consumption and client application activity
- **Tables**: oauthusagedata
- **API**: `/api/v2/oauth/clients`

#### SysConvUsage
- **Description**: Retrieves system conversation usage data for monitoring system activity
- **Tables**: systemcallusage
- **API**: `/api/v2/usage/query`

#### TeamsDetails
- **Description**: Retrieves team configuration data including team details and team membership
- **Tables**:
  - teamdetails
  - teammemberdata
- **APIs**:
  - `/api/v2/groups`
  - `/api/v2/groups/{groupId}/members`

### Knowledge and Learning Jobs

#### Knowledge
- **Description**: Retrieves knowledge base document data including content and metadata
- **Tables**: knowledgebasedocument
- **API**: `/api/v2/knowledge/knowledgebases/{knowledgeBaseId}/documents`

#### Learning
- **Description**: Retrieves learning assignment results data including completion status and scores
- **Tables**: learningassignmentresults
- **API**: `/api/v2/learning/assignments`

## Recent Updates

### Version 2025.01.x
- **New LicenseUsers Job**: Added comprehensive license and user data synchronization to replace deprecated SubsUsers functionality
- **Enhanced Deprecation Handling**: Implemented `GenesysApiDeprecatedException` for proper handling of deprecated API endpoints
- **Schema Consistency Improvements**: Standardized database schemas across MSSQL, PostgreSQL, and Snowflake platforms
- **Conditional Schema Logic**: Updated table creation to skip deprecated tables in new installations while preserving historical data
- **Safe subscriptiondata Handling**: Modified subscriptiondata to conditionally drop only if table exists and is empty, preserving any existing data
- **Restored scheduledata**: Corrected previous error - scheduledata is actively used by WFM functionality and has been restored
- **Improved Error Handling**: Enhanced API response validation with fail-fast behavior for deprecated endpoints
- **Architecture Documentation**: Added comprehensive Mermaid diagrams showing system architecture, project dependencies, data flow, database structure, and API integration patterns
- **Detailed Job Documentation**: Created comprehensive documentation for each job type with individual Mermaid diagrams, configuration guides, troubleshooting, and examples
- **Database Schema Optimizations**:
  - Fixed column size inconsistencies across database platforms
  - Corrected view definitions that were pointing to wrong tables
  - Optimized keyid column sizes for better performance
- **Backward Compatibility**: All changes maintain compatibility with existing installations while improving new deployments

### Migration Notes
- **From SubsUsers to LicenseUsers**: Existing customers using SubsUsers should migrate to LicenseUsers job for continued license monitoring
- **subscriptiondata Removal**: The subscriptiondata table has been removed as it was unused - no impact on functionality
- **scheduledata Restoration**: The scheduledata table has been restored as it's actively used by WFM functionality
- **Schema Updates**: Database schemas will automatically apply conditional logic during upgrades to handle deprecated tables appropriately
